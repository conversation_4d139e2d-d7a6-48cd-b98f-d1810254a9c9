"use client";

import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  ChevronDown,
  ChevronRight,
  Plus,
  Trash2,
  GripVertical,
  MoreHorizontal,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { RelationshipConfig } from "@/lib/schema/FormGenerator";
import { DynamicFieldRenderer } from "./DynamicFieldRenderer";
import { Field, Schema } from "@/lib/schema/types";
import { relationshipDisplayCache } from "@/lib/cache/RelationshipDisplayCache";

interface OneToManyManagerProps {
  relationship: RelationshipConfig;
  fieldName: string;
  relatedSchema: Schema;
  className?: string;
  parentId?: string; // The ID of the parent record for reference_id
  relatedSchemas?: Record<string, Schema>;
}

export const OneToManyManager: React.FC<OneToManyManagerProps> = ({
  relationship,
  fieldName,
  relatedSchema,
  className = "",
  parentId,
  relatedSchemas = {},
}) => {
  const { watch, setValue } = useFormContext();
  const items = watch(fieldName) || [];
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [displayValues, setDisplayValues] = useState<Record<number, string>>(
    {}
  );
  const [loadingDisplayValues, setLoadingDisplayValues] =
    useState<boolean>(false);

  // Load display values for all items when component mounts or items change
  useEffect(() => {
    const loadDisplayValues = async () => {
      if (items.length === 0) {
        setDisplayValues({});
        setLoadingDisplayValues(false);
        return;
      }

      setLoadingDisplayValues(true);
      const newDisplayValues: Record<number, string> = {};

      // Check if we can use batch fetching for relationship select fields
      const displayField =
        relatedSchema?.schema_definition?.table?.display_field || "id";
      const displayFieldDef = relatedSchema?.schema_definition?.fields?.find(
        (field: Field) => field.name === displayField
      );

      if (
        displayFieldDef?.ui_config?.widget === "relationship_select" &&
        displayFieldDef?.foreign_key &&
        items.some((item: any) => item[displayField])
      ) {
        // Use batch fetching for relationship select fields
        const tableName = displayFieldDef.foreign_key.hasura_table;
        const relationshipDisplayField =
          displayFieldDef.ui_config.display_field || "name";

        // Collect all unique values that need to be fetched
        const valuesToFetch = items
          .map((item: any) => item[displayField])
          .filter((value: any) => value && value.trim() !== "");

        if (valuesToFetch.length > 0) {
          try {
            const batchResults =
              await relationshipDisplayCache.getBatchDisplayValues(
                tableName,
                valuesToFetch,
                relationshipDisplayField,
                displayFieldDef.foreign_key.column
              );

            // Map the batch results back to item indices
            items.forEach((item: any, index: number) => {
              const itemValue = item[displayField];
              if (itemValue && batchResults[itemValue]) {
                newDisplayValues[index] = batchResults[itemValue];
              } else {
                newDisplayValues[index] = item.id || `Item ${index + 1}`;
              }
            });
          } catch (error) {
            console.error("Failed to load batch display values:", error);
            // Fallback to individual processing
            items.forEach((item: any, index: number) => {
              newDisplayValues[index] = item.id || `Item ${index + 1}`;
            });
          }
        } else {
          // No values to fetch, use fallback
          items.forEach((item: any, index: number) => {
            newDisplayValues[index] = item.id || `Item ${index + 1}`;
          });
        }
      } else {
        // Fallback to individual processing for non-relationship fields
        const displayValuePromises = items.map(
          async (item: Record<string, any>, index: number) => {
            try {
              const displayValue = await getDisplayValue(item);
              return { index, displayValue };
            } catch (error) {
              console.error("Failed to load display value for item:", error);
              return { index, displayValue: item.id || `Item ${index + 1}` };
            }
          }
        );

        const results = await Promise.all(displayValuePromises);
        results.forEach(({ index, displayValue }) => {
          newDisplayValues[index] = displayValue;
        });
      }

      setDisplayValues(newDisplayValues);
      setLoadingDisplayValues(false);
    };

    loadDisplayValues();
  }, [items]);

  const addItem = async () => {
    const newItem = await createEmptyItem(relatedSchema, parentId);
    setValue(fieldName, [...items, newItem], {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
    // Expand the newly added item
    setExpandedItems((prev) => new Set(prev).add(items.length));
  };

  const removeItem = (index: number) => {
    setValue(
      fieldName,
      items.filter((_: any, i: number) => i !== index),
      { shouldDirty: true, shouldTouch: true, shouldValidate: true }
    );
    // Remove from expanded items
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(index);
      // Adjust indices for items after the removed one
      const adjustedSet = new Set<number>();
      newSet.forEach((expandedIndex) => {
        if (expandedIndex < index) {
          adjustedSet.add(expandedIndex);
        } else if (expandedIndex > index) {
          adjustedSet.add(expandedIndex - 1);
        }
      });
      return adjustedSet;
    });
  };

  const updateItem = (index: number, updatedItem: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], ...updatedItem };
    setValue(fieldName, updatedItems, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const toggleItemExpansion = (index: number) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const createEmptyItem = async (schema: Schema, parentId?: string) => {
    const emptyItem: Record<string, any> = {};

    if (schema?.schema_definition?.fields) {
      for (const field of schema.schema_definition.fields) {
        if (field.default !== undefined) {
          emptyItem[field.name] = field.default;
        } else if (field.type === "boolean") {
          emptyItem[field.name] = false;
        } else if (field.type === "integer" || field.type === "decimal") {
          emptyItem[field.name] = field.validation?.min || 0;
        } else if (field.type === "text") {
          emptyItem[field.name] = "";
        }

        // Handle auto-population based on schema configuration
        if (field?.auto_populate && parentId) {
          const autoPopulate = field.auto_populate;

          if (autoPopulate.source === "parent_context") {
            emptyItem[field.name] = parentId;
          }
        }
      }
    }

    return emptyItem;
  };

  const getDisplayValue = async (item: Record<string, any>) => {
    // Try to find a suitable display field
    const displayField =
      relationship.displayField ||
      relatedSchema?.schema_definition?.table?.display_field ||
      relatedSchema?.schema_definition?.fields?.find(
        (f: Field) => f.name === relationship.sourceField
      )?.ui_config?.display_field ||
      relationship.sourceField ||
      "name";

    // Handle dot notation for nested fields
    if (displayField.includes(".")) {
      const key_list = displayField.split(".");
      let value = item;
      for (const key of key_list) {
        if (!value) {
          return "";
        }
        value = value[key];
      }
      return value;
    }

    // Check if the display field is a relationship select
    const displayFieldDef = relatedSchema?.schema_definition?.fields?.find(
      (f: Field) => f.name === displayField
    );

    if (
      displayFieldDef?.ui_config?.widget === "relationship_select" &&
      displayFieldDef?.foreign_key &&
      item[displayField]
    ) {
      // Use the global cache to get the display value
      const tableName = displayFieldDef.foreign_key.hasura_table;
      const relationshipDisplayField =
        displayFieldDef.ui_config.display_field || "name";

      try {
        const displayValue = await relationshipDisplayCache.getDisplayValue(
          tableName,
          item[displayField],
          relationshipDisplayField,
          displayFieldDef.foreign_key.column
        );
        return displayValue;
      } catch (error) {
        console.error("Failed to fetch relationship display value:", error);
        // Fall through to show the ID as fallback
      }
    }

    if (item[displayField]) {
      return item[displayField];
    }

    // Fallback to first text field
    if (relatedSchema?.schema_definition?.fields) {
      const textField = relatedSchema.schema_definition.fields.find(
        (f: Field) => f.type === "text" && f.name !== "id"
      );
      if (textField && item[textField.name]) {
        return item[textField.name];
      }
    }

    return item.id || `Item ${items.indexOf(item) + 1}`;
  };

  const moveItem = (fromIndex: number, toIndex: number) => {
    const updatedItems = [...items];
    const [movedItem] = updatedItems.splice(fromIndex, 1);
    updatedItems.splice(toIndex, 0, movedItem);
    setValue(fieldName, updatedItems, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });

    // Update expanded items indices
    setExpandedItems((prev) => {
      const newSet = new Set<number>();
      prev.forEach((expandedIndex) => {
        let newIndex = expandedIndex;
        if (expandedIndex === fromIndex) {
          newIndex = toIndex;
        } else if (expandedIndex < fromIndex && expandedIndex >= toIndex) {
          newIndex = expandedIndex + 1;
        } else if (expandedIndex > fromIndex && expandedIndex <= toIndex) {
          newIndex = expandedIndex - 1;
        }
        newSet.add(newIndex);
      });
      return newSet;
    });
  };

  return (
    <Card className={`border-l-4 border-l-blue-500 ${className}`}>
      <CardContent className="p-0">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800">
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 h-auto"
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            <h3 className="font-semibold text-sm">{relationship.title}</h3>
            <Badge variant="secondary" className="text-xs">
              {items.length}
            </Badge>
          </div>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addItem}
            className="flex items-center space-x-1 text-xs h-7"
          >
            <Plus className="h-3 w-3" />
            <span>Add an entry</span>
          </Button>
        </div>

        {/* Content */}
        {!isCollapsed && (
          <div className="p-4 space-y-3">
            {items.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p className="text-sm">
                  No {relationship.title.toLowerCase()} added yet
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addItem}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add first entry
                </Button>
              </div>
            ) : (
              items.map((item: Record<string, any>, index: number) => {
                const isExpanded = expandedItems.has(index);
                const displayValue =
                  displayValues[index] ||
                  (loadingDisplayValues
                    ? "Loading..."
                    : item.id || `Item ${index + 1}`);

                return (
                  <Card
                    key={item?.id || item?.name || index}
                    className="border border-slate-200 dark:border-slate-700"
                  >
                    {/* Item Header */}
                    <div className="flex items-center justify-between p-3 bg-slate-25 dark:bg-slate-900">
                      <div className="flex items-center space-x-2 flex-1">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleItemExpansion(index)}
                          className="p-1 h-auto"
                        >
                          {isExpanded ? (
                            <ChevronDown className="h-3 w-3" />
                          ) : (
                            <ChevronRight className="h-3 w-3" />
                          )}
                        </Button>

                        <div className="flex items-center space-x-2 flex-1">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">
                              {String.fromCharCode(65 + index)}
                            </span>
                          </div>
                          <span className="text-sm font-medium truncate">
                            {displayValue}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(index)}
                          className="p-1 h-auto text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="p-1 h-auto"
                            >
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => toggleItemExpansion(index)}
                            >
                              {isExpanded ? "Collapse" : "Expand"}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                moveItem(index, Math.max(0, index - 1))
                              }
                              disabled={index === 0}
                            >
                              Move Up
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                moveItem(
                                  index,
                                  Math.min(items.length - 1, index + 1)
                                )
                              }
                              disabled={index === items.length - 1}
                            >
                              Move Down
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => removeItem(index)}
                              className="text-destructive"
                            >
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <div className="cursor-grab hover:cursor-grabbing">
                          <GripVertical className="h-3 w-3 text-muted-foreground" />
                        </div>
                      </div>
                    </div>

                    {/* Item Content */}
                    {isExpanded && relatedSchema && (
                      <div className="p-4 border-t border-slate-200 dark:border-slate-700">
                        <DynamicFieldRenderer
                          schema={relatedSchema}
                          item={item}
                          itemIndex={index}
                          fieldName={fieldName}
                          onUpdate={(updatedItem) =>
                            updateItem(index, updatedItem)
                          }
                          relatedSchemas={relatedSchemas}
                          parentId={parentId}
                          mode="edit"
                        />
                      </div>
                    )}
                  </Card>
                );
              })
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
