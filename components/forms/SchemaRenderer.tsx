"use client";

import React, { useRef } from "react";
import { FieldRenderer } from "@/components/forms/FieldRenderer";
import { RelationshipRenderer } from "@/components/forms/RelationshipRenderer";
import { FormSection } from "@/components/forms/FormSection";
import { PreviewUrlWidget } from "@/components/forms/widgets/PreviewUrlWidget";
import { FormConfig } from "@/lib/schema/FormGenerator";
import { FormSection as FormSectionType } from "@/lib/schema/types";

import { Button } from "@/components/ui/button";

interface SchemaRendererProps {
  config: FormConfig;
  sections?: string[];
  relationships?: string[];
  parentId?: string;
}

interface RelationshipNavigationProps {
  relationships: any[];
  relatedSchemas: Record<string, any>;
  mode?: "create" | "edit";
  parentId?: string;
}

const RelationshipNavigation: React.FC<RelationshipNavigationProps> = ({
  relationships,
  relatedSchemas,
  mode,
  parentId,
}) => {
  const relationshipRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const scrollToRelationship = (relationshipName: string) => {
    const element = relationshipRefs.current[relationshipName];
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  };

  if (relationships.length === 0) return null;

  return (
    <div className="mt-8">
      {/* Relationship Navigation */}
      {relationships.length > 1 && (
        <div className="sticky top-4 z-20 bg-background/95 backdrop-blur-sm border border-border rounded-lg shadow-sm mb-8 p-4">
          <div className="flex flex-wrap gap-3">
            {relationships.map((rel) => (
              <Button
                key={rel.name}
                variant="outline"
                size="sm"
                onClick={() => scrollToRelationship(rel.name)}
                className="text-sm font-medium hover:bg-primary/10 hover:border-primary/20 transition-colors"
              >
                {rel.title}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Relationship Content */}
      <div className="space-y-6">
        {relationships.map((rel) => {
          const relatedSchema = relatedSchemas?.[rel.targetComponent];
          if (!relatedSchema) {
            return null;
          }
          return (
            <div
              key={rel.name}
              ref={(el) => {
                relationshipRefs.current[rel.name] = el;
              }}
              className="scroll-mt-24"
            >
              <RelationshipRenderer
                relationship={rel}
                relatedSchema={relatedSchema}
                parentId={parentId}
                mode={mode}
                relatedSchemas={relatedSchemas}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export const SchemaRenderer: React.FC<SchemaRendererProps> = ({
  config,
  sections,
  relationships,
  parentId,
}) => {
  const { fields, layout, previewUrl } = config;

  if (sections || relationships) {
    // Tab-based rendering
    const tabRelationships = relationships
      ? relationships.map((relName) => config.relationships.find((r) => r.name === relName)).filter(Boolean)
      : [];

    return (
      <div className="space-y-6">
        {previewUrl && (
          <div className="mt-6">
            <PreviewUrlWidget config={previewUrl} />
          </div>
        )}

        {sections &&
          sections.map((sectionName) => {
            const section = layout.sections?.find(
              (s: FormSectionType) => s.title === sectionName
            );

            return section ? (
              <FormSection
                key={sectionName}
                section={section}
                fields={fields}
              />
            ) : null;
          })}

        {/* Render relationships with navigation if there are any */}
        {tabRelationships.length > 0 && (
          <RelationshipNavigation
            relationships={tabRelationships}
            relatedSchemas={config.relatedSchemas || {}}
            mode={config.mode}
            parentId={parentId}
          />
        )}
      </div>
    );
  }

  // Section-based rendering
  if (layout?.sections) {
    return (
      <div className="space-y-6">
        {previewUrl && (
          <div className="mt-6">
            <PreviewUrlWidget config={previewUrl} />
          </div>
        )}

        {layout.sections.map((section: FormSectionType, index: number) => (
          <div key={section.title} className={index > 0 ? "mt-6" : ""}>
            <FormSection section={section} fields={fields} />
          </div>
        ))}

        <RelationshipNavigation
          relationships={config.relationships}
          relatedSchemas={config.relatedSchemas || {}}
          mode={config.mode}
          parentId={parentId}
        />
      </div>
    );
  }

  // Default rendering (no layout)
  return (
    <div className="space-y-6">
      {previewUrl && (
        <div className="mt-6">
          <PreviewUrlWidget config={previewUrl} />
        </div>
      )}

      <div className="grid gap-4">
        {fields
          .filter((field) => !field.hidden)
          .map((field) => (
            <div key={field.name} className="col-span-12">
              {field.name}
              <FieldRenderer field={field} />
            </div>
          ))}
      </div>

      <RelationshipNavigation
        relationships={config.relationships}
        relatedSchemas={config.relatedSchemas || {}}
        mode={config.mode}
        parentId={parentId}
      />
    </div>
  );
};
