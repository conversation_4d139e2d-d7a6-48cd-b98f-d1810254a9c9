"use client";

import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  Plus,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RelationshipConfig } from "@/lib/schema/FormGenerator";
import { DynamicFieldRenderer } from "./DynamicFieldRenderer";
import { Field, Schema } from "@/lib/schema/types";
import { relationshipDisplayCache } from "@/lib/cache/RelationshipDisplayCache";

interface OneToOneManagerProps {
  relationship: RelationshipConfig;
  fieldName: string;
  relatedSchema: Schema;
  className?: string;
  parentId?: string; // The ID of the parent record for reference_id
  relatedSchemas?: Record<string, Schema>;
}

export const OneToOneManager: React.FC<OneToOneManagerProps> = ({
  relationship,
  fieldName,
  relatedSchema,
  className = "",
  parentId,
  relatedSchemas = {},
}) => {
  const { watch, setValue } = useFormContext();
  const item = watch(fieldName) || null;
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [displayValue, setDisplayValue] = useState<string>("");
  const [hasChanges, setHasChanges] = useState(false);
  const [initialItem, setInitialItem] = useState<any>(null);

  // Track initial item state for change detection
  useEffect(() => {
    if (item && !initialItem) {
      setInitialItem(JSON.parse(JSON.stringify(item)));
    }
  }, [item, initialItem]);

  // Detect changes by comparing current item with initial item
  useEffect(() => {
    if (item && initialItem) {
      const hasItemChanges = JSON.stringify(item) !== JSON.stringify(initialItem);
      setHasChanges(hasItemChanges);
    } else {
      setHasChanges(false);
    }
  }, [item, initialItem]);

  // Load display value for the item
  useEffect(() => {
    const loadDisplayValue = async () => {
      if (item) {
        try {
          const value = await getDisplayValue(item);
          setDisplayValue(value);
        } catch (error) {
          console.error("Failed to load display value for item:", error);
          setDisplayValue(item.id || "Item");
        }
      } else {
        setDisplayValue("");
      }
    };

    loadDisplayValue();
  }, [item, relatedSchema, relationship]);

  const addItem = async () => {
    const newItem = await createEmptyItem(relatedSchema, parentId);
    setValue(fieldName, newItem, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
    // Reset initial item state for new item
    setInitialItem(JSON.parse(JSON.stringify(newItem)));
    setHasChanges(false);
  };

  const updateItem = (updatedItem: any) => {
    setValue(fieldName, updatedItem, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const createEmptyItem = async (schema: Schema, parentId?: string) => {
    const emptyItem: Record<string, any> = {};

    if (schema?.schema_definition?.fields) {
      for (const field of schema.schema_definition.fields) {
        if (field.default !== undefined) {
          emptyItem[field.name] = field.default;
        } else if (field.type === "boolean") {
          emptyItem[field.name] = false;
        } else if (field.type === "integer" || field.type === "decimal") {
          emptyItem[field.name] = field.validation?.min || 0;
        } else if (field.type === "text") {
          emptyItem[field.name] = "";
        }

        // Handle auto-population based on schema configuration
        if (field?.auto_populate && parentId) {
          const autoPopulate = field.auto_populate;

          if (autoPopulate.source === "parent_context") {
            emptyItem[field.name] = parentId;
          }
        }
      }
    }

    return emptyItem;
  };

  const getDisplayValue = async (item: Record<string, any>) => {
    // Try to find a suitable display field
    const displayField =
      relationship.displayField ||
      relatedSchema?.schema_definition?.table?.display_field ||
      relatedSchema?.schema_definition?.fields?.find(
        (f: Field) => f.name === relationship.sourceField
      )?.ui_config?.display_field ||
      relationship.sourceField ||
      "name";

    // Handle dot notation for nested fields
    if (displayField.includes(".")) {
      const key_list = displayField.split(".");
      let value = item;
      for (const key of key_list) {
        if (!value) {
          return "";
        }
        value = value[key];
      }
      return value;
    }

    // Check if the display field is a relationship select
    const displayFieldDef = relatedSchema?.schema_definition?.fields?.find(
      (f: Field) => f.name === displayField
    );

    if (displayFieldDef?.ui_config?.widget === "relationship_select" &&
        displayFieldDef?.foreign_key &&
        item[displayField]) {

      // Use the global cache to get the display value
      const tableName = displayFieldDef.foreign_key.hasura_table;
      const relationshipDisplayField = displayFieldDef.ui_config.display_field || "name";

      try {
        const displayValue = await relationshipDisplayCache.getDisplayValue(
          tableName,
          item[displayField],
          relationshipDisplayField,
          displayFieldDef.foreign_key.column
        );
        return displayValue;
      } catch (error) {
        console.error("Failed to fetch relationship display value:", error);
        // Fall through to show the ID as fallback
      }
    }

    if (item[displayField]) {
      return item[displayField];
    }

    // Fallback to first text field
    if (relatedSchema?.schema_definition?.fields) {
      const textField = relatedSchema.schema_definition.fields.find(
        (f: Field) => f.type === "text" && f.name !== "id"
      );
      if (textField && item[textField.name]) {
        return item[textField.name];
      }
    }

    return item.id || "New Item";
  };

  return (
    <Card className={`border-l-4 ${hasChanges ? 'border-l-orange-500' : 'border-l-green-500'} ${className}`}>
      <CardContent className="p-0">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800">
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 h-auto"
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            <h3 className="font-semibold text-sm">{relationship.title}</h3>
            <Badge variant="secondary" className="text-xs">
              {item ? "1" : "0"}
            </Badge>
          </div>

          <div className="flex items-center space-x-2">
            {!item && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addItem}
                className="flex items-center space-x-1 text-xs h-7"
              >
                <Plus className="h-3 w-3" />
                <span>Add {relationship.title}</span>
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        {!isCollapsed && (
          <div className="p-4">
            {!item ? (
              <div className="text-center py-8 text-muted-foreground">
                <p className="text-sm">
                  No {relationship.title.toLowerCase()} added yet
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addItem}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add {relationship.title}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Item Display Header */}
                <div className="flex items-center p-3 bg-slate-25 dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">1</span>
                    </div>
                    <span className="text-sm font-medium">
                      {displayValue || item.id || "Item"}
                    </span>
                  </div>
                </div>

                {/* Item Content - Always Visible */}
                {relatedSchema && (
                  <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg bg-white dark:bg-slate-900">
                    <DynamicFieldRenderer
                      schema={relatedSchema}
                      item={item}
                      itemIndex={null}
                      fieldName={fieldName}
                      onUpdate={updateItem}
                      relatedSchemas={relatedSchemas}
                      parentId={parentId}
                      mode="edit"
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};